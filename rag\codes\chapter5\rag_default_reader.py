# 导入操作系统相关模块
import os
# 导入LazyLLM主模块
import lazyllm
# 导入RAG文档处理模块
from lazyllm.tools.rag import Document
# 导入句子分割器
from lazyllm import SentenceSplitter
# 导入流水线、并行处理、检索器、重排序器和绑定功能
from lazyllm import pipeline, parallel, Retriever, Reranker, bind


# 定义AI问答助手的提示词模板
# 指导AI扮演问答助手角色，基于给定上下文和问题提供答案
prompt = ('You will play the role of an AI Q&A assistant and complete a dialogue task. '
          'In this task, you need to provide your answer based on the given context and question.')

# 创建文档对象，使用默认的文档读取器
# dataset_path: 指定数据集路径为当前目录下的rag_data文件夹
# embed: 使用GLM的在线嵌入模型embedding-2进行文档向量化
# manager: 设置为False，不使用文档管理器
documents = Document(dataset_path=os.path.join(os.getcwd(), "rag_data"),
                     embed=lazyllm.OnlineEmbeddingModule(source="glm", embed_model_name="embedding-2"), manager=False)

# 创建文档节点组，用于句子级别的文档分割
# name: 节点组名称为"sentences"
# transform: 使用SentenceSplitter进行文本分割
# chunk_size: 每个文档块的大小为1024个字符
# chunk_overlap: 文档块之间的重叠为100个字符，确保上下文连续性
documents.create_node_group(name="sentences", transform=SentenceSplitter, chunk_size=1024, chunk_overlap=100)

# 构建RAG流水线
with pipeline() as ppl:
    # 并行检索阶段：使用两个不同的检索器
    with parallel().sum as ppl.prl:
        # 检索器1：基于句子级别的余弦相似度检索
        # group_name: 使用"sentences"节点组
        # similarity: 使用余弦相似度计算
        # topk: 返回前3个最相关的文档
        ppl.prl.retriever1 = Retriever(documents, group_name="sentences", similarity="cosine", topk=3)

        # 检索器2：基于粗粒度块的BM25中文检索
        # "CoarseChunk": 使用粗粒度文档块
        # "bm25_chinese": 使用BM25中文算法
        # 0.003: BM25算法的阈值参数
        # topk: 返回前3个最相关的文档
        ppl.prl.retriever2 = Retriever(documents, "CoarseChunk", "bm25_chinese", 0.003, topk=3)

    # 重排序阶段：对检索结果进行重新排序
    # ModuleReranker: 使用模块化重排序器
    # model: 使用GLM的在线重排序模型
    # topk: 最终返回1个最相关的文档
    # output_format: 输出格式为内容
    # join: 将多个结果合并
    # bind: 绑定查询输入
    ppl.reranker = Reranker("ModuleReranker",
                            model=lazyllm.OnlineEmbeddingModule(type="rerank", source="glm",
                                                                embed_model_name="rerank"),
                            topk=1, output_format='content', join=True) | bind(query=ppl.input)

    # 格式化阶段：将检索到的节点和查询格式化为字典
    # lambda函数：接收nodes和query参数，返回包含context_str和query的字典
    # bind: 绑定查询输入
    ppl.formatter = (lambda nodes, query: dict(context_str=nodes, query=query)) | bind(query=ppl.input)

    # 大语言模型阶段：使用GLM-4模型生成最终答案
    # source: 使用GLM作为模型源
    # model: 指定使用glm-4模型
    # stream: 设置为False，不使用流式输出
    # prompt: 使用ChatPrompter配置提示词，extra_keys指定额外的上下文键
    ppl.llm = lazyllm.OnlineChatModule(source='glm',
                                       model="glm-4",
                                       stream=False).prompt(lazyllm.ChatPrompter(prompt, extra_keys=["context_str"]))

# 测试RAG系统：询问关于全国住房城乡建设工作会议的主要内容
print(ppl("全国住房城乡建设工作会议的主要内容"))
